"""
Production Overhead Per Kg Calculation Demo
This script demonstrates how to calculate per kg production overhead
"""

def calculate_production_overhead():
    """
    Calculate per kg production overhead with sample data
    """
    
    print("="*60)
    print("PRODUCTION OVERHEAD PER KG CALCULATION")
    print("="*60)
    
    # Sample Production Overhead Components
    overhead_components = {
        "Factory Rent": 50000,
        "Utilities (Electricity, Water)": 25000,
        "Depreciation on Machinery": 30000,
        "Maintenance & Repairs": 15000,
        "Indirect Labor": 40000,
        "Factory Insurance": 8000,
        "Other Factory Expenses": 12000
    }
    
    print("\n1. PRODUCTION OVERHEAD COMPONENTS:")
    print("-" * 40)
    total_overhead = 0
    for component, amount in overhead_components.items():
        print(f"{component:<30}: ₹{amount:>10,}")
        total_overhead += amount
    
    print("-" * 40)
    print(f"{'TOTAL PRODUCTION OVERHEAD':<30}: ₹{total_overhead:>10,}")
    
    # Sample Product Data
    products = {
        "Product A": {"quantity": 1000, "rate": 50},
        "Product B": {"quantity": 1500, "rate": 75},
        "Product C": {"quantity": 800, "rate": 60},
        "Product D": {"quantity": 1200, "rate": 45},
        "Product E": {"quantity": 2000, "rate": 55}
    }
    
    print("\n\n2. PRODUCT QUANTITY DATA:")
    print("-" * 50)
    print(f"{'Product':<12} {'Quantity (kg)':<15} {'Rate/kg':<10} {'Amount':<12}")
    print("-" * 50)
    
    total_quantity = 0
    total_amount = 0
    
    for product, data in products.items():
        quantity = data["quantity"]
        rate = data["rate"]
        amount = quantity * rate
        total_quantity += quantity
        total_amount += amount
        
        print(f"{product:<12} {quantity:>10,} kg {rate:>8} ₹{amount:>10,}")
    
    print("-" * 50)
    print(f"{'TOTAL':<12} {total_quantity:>10,} kg {'':<8} ₹{total_amount:>10,}")
    
    # Calculate Per Kg Production Overhead
    per_kg_overhead = total_overhead / total_quantity
    
    print("\n\n3. PER KG PRODUCTION OVERHEAD CALCULATION:")
    print("-" * 50)
    print(f"Total Production Overhead: ₹{total_overhead:,}")
    print(f"Total Production Quantity: {total_quantity:,} kg")
    print(f"Per Kg Production Overhead: ₹{per_kg_overhead:.2f}")
    
    print("\n\nFORMULA:")
    print(f"Per Kg OH = Total Production Overhead ÷ Total Production Quantity")
    print(f"Per Kg OH = ₹{total_overhead:,} ÷ {total_quantity:,} kg")
    print(f"Per Kg OH = ₹{per_kg_overhead:.2f} per kg")
    
    # Calculate overhead allocation for each product
    print("\n\n4. OVERHEAD ALLOCATION BY PRODUCT:")
    print("-" * 70)
    print(f"{'Product':<12} {'Quantity':<12} {'OH per kg':<12} {'Total OH':<15} {'Total Cost':<12}")
    print("-" * 70)
    
    for product, data in products.items():
        quantity = data["quantity"]
        rate = data["rate"]
        direct_cost = quantity * rate
        overhead_allocation = quantity * per_kg_overhead
        total_cost = direct_cost + overhead_allocation
        
        print(f"{product:<12} {quantity:>8} kg ₹{per_kg_overhead:>8.2f} ₹{overhead_allocation:>12,.2f} ₹{total_cost:>10,.2f}")
    
    print("\n\n5. EXCEL FORMULA FOR BLANK COLUMN:")
    print("-" * 40)
    print("If your blank column should contain 'Production Overhead per kg':")
    print(f"Use this value: ₹{per_kg_overhead:.2f}")
    print("\nExcel Formula for each product row:")
    print("=Quantity_in_kg * Per_Kg_OH_Rate")
    print(f"=Quantity_in_kg * {per_kg_overhead:.2f}")
    
    print("\n\n6. COST SHEET INTEGRATION:")
    print("-" * 40)
    print("To fill the blank column in your cost sheet:")
    print("1. Use the calculated per kg overhead rate")
    print("2. Multiply by each product's quantity in kg")
    print("3. This gives the total overhead allocation per product")
    
    return per_kg_overhead, total_overhead, total_quantity

def create_excel_formulas():
    """
    Display Excel formulas for implementation
    """
    print("\n\n" + "="*60)
    print("EXCEL FORMULAS FOR IMPLEMENTATION")
    print("="*60)
    
    formulas = [
        ("Total Production Overhead", "=SUM(B4:B10)", "Sum of all overhead components"),
        ("Total Production Quantity", "=SUM(B2:B6)", "Sum of all product quantities"),
        ("Per Kg Production Overhead", "=B12/B14", "Total OH divided by Total Quantity"),
        ("Product Overhead Allocation", "=B2*$B$15", "Quantity × Per Kg OH Rate"),
        ("Total Product Cost", "=C2+D2+E2", "Direct Cost + Labor + Overhead")
    ]
    
    print(f"\n{'Description':<30} {'Formula':<20} {'Explanation'}")
    print("-" * 80)
    
    for desc, formula, explanation in formulas:
        print(f"{desc:<30} {formula:<20} {explanation}")

if __name__ == "__main__":
    # Run the calculation
    per_kg_oh, total_oh, total_qty = calculate_production_overhead()
    
    # Show Excel formulas
    create_excel_formulas()
    
    print(f"\n\nSUMMARY:")
    print(f"Per Kg Production Overhead: ₹{per_kg_oh:.2f}")
    print(f"Use this value to fill your blank column in the cost sheet.")
