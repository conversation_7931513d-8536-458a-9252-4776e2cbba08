import pandas as pd
import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter
import os

def create_production_overhead_calculator():
    """
    Create a comprehensive Excel workbook for calculating per kg production overhead
    """
    
    # Create a new workbook
    wb = openpyxl.Workbook()
    
    # Remove default sheet
    wb.remove(wb.active)
    
    # Create sheets
    data_sheet = wb.create_sheet("Product Data")
    overhead_sheet = wb.create_sheet("Overhead Calculation")
    cost_sheet = wb.create_sheet("Cost Sheet")
    formula_sheet = wb.create_sheet("Formulas & Instructions")
    
    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    border = Border(left=Side(style='thin'), right=Side(style='thin'), 
                   top=Side(style='thin'), bottom=Side(style='thin'))
    center_align = Alignment(horizontal="center", vertical="center")
    
    # ===== PRODUCT DATA SHEET =====
    data_sheet.title = "Product Data"
    
    # Headers for product data
    headers = ["Product Item", "Quantity (kg)", "Average Rate (per kg)", "Amount", "Production OH per kg", "Total Cost per kg"]
    for col, header in enumerate(headers, 1):
        cell = data_sheet.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = center_align
    
    # Sample data
    sample_data = [
        ["Product A", 1000, 50, "=B2*C2", "=Overhead_Calculation.B$15", "=C2+E2"],
        ["Product B", 1500, 75, "=B3*C3", "=Overhead_Calculation.B$15", "=C3+E3"],
        ["Product C", 800, 60, "=B4*C4", "=Overhead_Calculation.B$15", "=C4+E4"],
        ["Product D", 1200, 45, "=B5*C5", "=Overhead_Calculation.B$15", "=C5+E5"],
        ["Product E", 2000, 55, "=B6*C6", "=Overhead_Calculation.B$15", "=C6+E6"],
    ]
    
    for row, data in enumerate(sample_data, 2):
        for col, value in enumerate(data, 1):
            cell = data_sheet.cell(row=row, column=col, value=value)
            cell.border = border
            if col in [2, 3, 4, 5, 6]:  # Numeric columns
                cell.number_format = '#,##0.00'
    
    # Total row
    total_row = len(sample_data) + 2
    data_sheet.cell(row=total_row, column=1, value="TOTAL").font = Font(bold=True)
    data_sheet.cell(row=total_row, column=2, value=f"=SUM(B2:B{total_row-1})").font = Font(bold=True)
    data_sheet.cell(row=total_row, column=4, value=f"=SUM(D2:D{total_row-1})").font = Font(bold=True)
    
    # ===== OVERHEAD CALCULATION SHEET =====
    overhead_sheet.title = "Overhead Calculation"
    
    # Overhead components
    overhead_data = [
        ["PRODUCTION OVERHEAD COMPONENTS", ""],
        ["", ""],
        ["Overhead Item", "Amount"],
        ["Factory Rent", 50000],
        ["Utilities (Electricity, Water)", 25000],
        ["Depreciation on Machinery", 30000],
        ["Maintenance & Repairs", 15000],
        ["Indirect Labor", 40000],
        ["Factory Insurance", 8000],
        ["Other Factory Expenses", 12000],
        ["", ""],
        ["TOTAL PRODUCTION OVERHEAD", "=SUM(B4:B10)"],
        ["", ""],
        ["TOTAL PRODUCTION QUANTITY (kg)", f"='Product Data'.B{total_row}"],
        ["PER KG PRODUCTION OVERHEAD", "=B12/B14"],
    ]
    
    for row, (item, amount) in enumerate(overhead_data, 1):
        overhead_sheet.cell(row=row, column=1, value=item)
        if amount:
            overhead_sheet.cell(row=row, column=2, value=amount)
        
        # Style headers and totals
        if row in [1, 3, 12, 14, 15]:
            overhead_sheet.cell(row=row, column=1).font = Font(bold=True)
            overhead_sheet.cell(row=row, column=2).font = Font(bold=True)
        
        if row in [3, 12, 15]:
            overhead_sheet.cell(row=row, column=1).fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
            overhead_sheet.cell(row=row, column=2).fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    
    # Format numbers
    for row in range(4, 16):
        cell = overhead_sheet.cell(row=row, column=2)
        if cell.value:
            cell.number_format = '#,##0.00'
    
    # ===== COST SHEET =====
    cost_sheet.title = "Cost Sheet"
    
    # Cost sheet headers
    cost_headers = ["Product Item", "Quantity (kg)", "Direct Material Cost", "Direct Labor Cost", 
                   "Production OH per kg", "Total Production OH", "Total Cost", "Cost per kg"]
    
    for col, header in enumerate(cost_headers, 1):
        cell = cost_sheet.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = center_align
    
    # Cost sheet data (referencing product data)
    for row in range(2, 7):  # 5 products
        cost_sheet.cell(row=row, column=1, value=f"='Product Data'.A{row}")  # Product name
        cost_sheet.cell(row=row, column=2, value=f"='Product Data'.B{row}")  # Quantity
        cost_sheet.cell(row=row, column=3, value=f"='Product Data'.C{row}*B{row}")  # Direct material cost
        cost_sheet.cell(row=row, column=4, value=f"=C{row}*0.2")  # Assume labor is 20% of material
        cost_sheet.cell(row=row, column=5, value=f"='Overhead Calculation'.B$15")  # OH per kg
        cost_sheet.cell(row=row, column=6, value=f"=E{row}*B{row}")  # Total OH
        cost_sheet.cell(row=row, column=7, value=f"=C{row}+D{row}+F{row}")  # Total cost
        cost_sheet.cell(row=row, column=8, value=f"=G{row}/B{row}")  # Cost per kg
    
    # Format cost sheet
    for row in range(2, 7):
        for col in range(2, 9):
            cell = cost_sheet.cell(row=row, column=col)
            cell.border = border
            if col != 1:
                cell.number_format = '#,##0.00'
    
    # ===== FORMULAS & INSTRUCTIONS SHEET =====
    formula_sheet.title = "Formulas & Instructions"
    
    instructions = [
        ["PRODUCTION OVERHEAD CALCULATION FORMULAS", ""],
        ["", ""],
        ["1. Per Kg Production Overhead Formula:", ""],
        ["   Per Kg OH = Total Production Overhead ÷ Total Production Quantity (kg)", ""],
        ["", ""],
        ["2. Total Production Overhead Calculation:", ""],
        ["   Sum of all indirect manufacturing costs:", ""],
        ["   - Factory Rent", ""],
        ["   - Utilities", ""],
        ["   - Depreciation", ""],
        ["   - Maintenance", ""],
        ["   - Indirect Labor", ""],
        ["   - Insurance", ""],
        ["   - Other Factory Expenses", ""],
        ["", ""],
        ["3. Product Cost Calculation:", ""],
        ["   Total Cost = Direct Material + Direct Labor + Production Overhead", ""],
        ["   Cost per kg = Total Cost ÷ Quantity (kg)", ""],
        ["", ""],
        ["4. Instructions:", ""],
        ["   - Update overhead amounts in 'Overhead Calculation' sheet", ""],
        ["   - Update product quantities and rates in 'Product Data' sheet", ""],
        ["   - The per kg overhead will automatically calculate", ""],
        ["   - All formulas are linked and will update automatically", ""],
        ["", ""],
        ["5. Key Cells:", ""],
        ["   - Total Production Overhead: Overhead Calculation!B12", ""],
        ["   - Total Quantity: Product Data!B7 (sum of all quantities)", ""],
        ["   - Per Kg OH: Overhead Calculation!B15", ""],
    ]
    
    for row, (instruction, _) in enumerate(instructions, 1):
        cell = formula_sheet.cell(row=row, column=1, value=instruction)
        if row in [1, 3, 6, 16, 20, 26]:
            cell.font = Font(bold=True, color="0066CC")
    
    # Adjust column widths
    for sheet in [data_sheet, overhead_sheet, cost_sheet, formula_sheet]:
        for column in sheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            sheet.column_dimensions[column_letter].width = adjusted_width
    
    # Save the workbook
    filename = "Production_Overhead_Calculator.xlsx"
    wb.save(filename)
    print(f"Excel file '{filename}' created successfully!")
    
    return filename

if __name__ == "__main__":
    create_production_overhead_calculator()
