# Production Overhead Per Kg Calculation Guide

## Overview
This guide explains how to calculate the per kg production overhead for your cost sheet and provides the necessary Excel tools and formulas.

## Formula for Per Kg Production Overhead

### Basic Formula:
```
Per Kg Production Overhead = Total Production Overhead ÷ Total Production Quantity (kg)
```

### Detailed Calculation Steps:

1. **Calculate Total Production Overhead**
   ```
   Total Production Overhead = Sum of all indirect manufacturing costs
   ```
   
   Components typically include:
   - Factory Rent
   - Utilities (Electricity, Water, Gas)
   - Depreciation on Machinery and Equipment
   - Maintenance and Repairs
   - Indirect Labor (Supervisors, Quality Control)
   - Factory Insurance
   - Other Factory Expenses

2. **Calculate Total Production Quantity**
   ```
   Total Production Quantity = Sum of all product quantities in kg
   ```

3. **Calculate Per Kg Overhead**
   ```
   Per Kg OH = Total Production Overhead ÷ Total Production Quantity
   ```

## Example Calculation

### Sample Data:
- **Production Overhead Components:**
  - Factory Rent: ₹50,000
  - Utilities: ₹25,000
  - Depreciation: ₹30,000
  - Maintenance: ₹15,000
  - Indirect Labor: ₹40,000
  - Insurance: ₹8,000
  - Other Expenses: ₹12,000
  - **Total Production Overhead: ₹180,000**

- **Product Quantities:**
  - Product A: 1,000 kg
  - Product B: 1,500 kg
  - Product C: 800 kg
  - Product D: 1,200 kg
  - Product E: 2,000 kg
  - **Total Quantity: 6,500 kg**

### Calculation:
```
Per Kg Production Overhead = ₹180,000 ÷ 6,500 kg = ₹27.69 per kg
```

## Excel Implementation

### Files Created:
1. **Production_Overhead_Calculator.xlsx** - Complete calculator with 4 sheets:
   - **Product Data**: Input product quantities, rates, and amounts
   - **Overhead Calculation**: Calculate total overhead and per kg rate
   - **Cost Sheet**: Complete cost calculation including overhead
   - **Formulas & Instructions**: Detailed formulas and usage guide

### Key Excel Formulas:

1. **Total Production Overhead:**
   ```excel
   =SUM(B4:B10)
   ```

2. **Total Production Quantity:**
   ```excel
   =SUM('Product Data'.B2:B6)
   ```

3. **Per Kg Production Overhead:**
   ```excel
   =Total_Overhead/Total_Quantity
   ```

4. **Product Total Cost:**
   ```excel
   =Direct_Material_Cost + Direct_Labor_Cost + (Quantity * Per_Kg_OH)
   ```

## How to Use the Excel Calculator

### Step 1: Update Overhead Costs
- Go to "Overhead Calculation" sheet
- Update the overhead amounts in column B (rows 4-10)
- The total will automatically calculate

### Step 2: Update Product Data
- Go to "Product Data" sheet
- Update product names, quantities, and average rates
- The amounts and overhead allocations will automatically calculate

### Step 3: Review Cost Sheet
- Go to "Cost Sheet" sheet
- Review the complete cost breakdown for each product
- All calculations are automatically linked

### Step 4: Fill Blank Column in Your Original Cost Sheet
- Use the calculated "Per Kg Production Overhead" value
- Apply this rate to each product based on its quantity
- Formula: `Product Quantity (kg) × Per Kg OH Rate`

## Integration with Your Existing Cost Sheet

To fill the blank column in your existing cost sheet:

1. **Identify the blank column** that needs production overhead per kg
2. **Use the calculated rate** from the Overhead Calculation sheet (₹27.69 per kg in the example)
3. **Apply the formula** in each row:
   ```excel
   =Product_Quantity_kg * Per_Kg_OH_Rate
   ```

## Benefits of This Approach

1. **Accurate Cost Allocation**: Distributes overhead costs proportionally based on production volume
2. **Automatic Updates**: When overhead costs or quantities change, all calculations update automatically
3. **Transparency**: Clear breakdown of all overhead components
4. **Scalability**: Easy to add new products or overhead categories
5. **Audit Trail**: All formulas are visible and traceable

## Notes

- Ensure all quantities are in the same unit (kg)
- Update overhead costs regularly to reflect current expenses
- Consider seasonal variations in overhead costs
- Review and validate calculations periodically
- Keep backup copies of your cost sheets

## Support

For any questions about the calculations or Excel implementation, refer to the "Formulas & Instructions" sheet in the Excel file for detailed guidance.
