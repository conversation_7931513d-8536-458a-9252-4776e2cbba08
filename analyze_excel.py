import pandas as pd
import openpyxl
import sys

def analyze_excel_file(filename):
    try:
        # Try to read with pandas first
        print("=== Reading with Pandas ===")
        df = pd.read_excel(filename, sheet_name=None)  # Read all sheets
        
        for sheet_name, sheet_df in df.items():
            print(f"\nSheet: {sheet_name}")
            print(f"Shape: {sheet_df.shape}")
            print(f"Columns: {sheet_df.columns.tolist()}")
            print("\nFirst 5 rows:")
            print(sheet_df.head())
            
            # Check for blank columns
            blank_cols = []
            for col in sheet_df.columns:
                if sheet_df[col].isna().all():
                    blank_cols.append(col)
            
            if blank_cols:
                print(f"Blank columns found: {blank_cols}")
            
            print("\n" + "="*50)
        
    except Exception as e:
        print(f"Error with pandas: {e}")
        
    try:
        # Try with openpyxl for more detailed analysis
        print("\n=== Reading with OpenPyXL ===")
        wb = openpyxl.load_workbook(filename)
        print(f"Worksheets: {wb.sheetnames}")
        
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            print(f"\nSheet: {sheet_name}")
            print(f"Max row: {ws.max_row}, Max col: {ws.max_column}")
            
            # Read header row
            headers = []
            for col in range(1, ws.max_column + 1):
                cell_value = ws.cell(row=1, column=col).value
                headers.append(cell_value)
            print(f"Headers: {headers}")
            
            # Check for blank columns
            for col in range(1, ws.max_column + 1):
                col_values = []
                for row in range(2, min(ws.max_row + 1, 20)):  # Check first 20 rows
                    cell_value = ws.cell(row=row, column=col).value
                    col_values.append(cell_value)
                
                if all(v is None for v in col_values):
                    col_letter = openpyxl.utils.get_column_letter(col)
                    print(f"Blank column found: {col_letter} ('{headers[col-1]}')")
            
    except Exception as e:
        print(f"Error with openpyxl: {e}")

if __name__ == "__main__":
    analyze_excel_file("Producton qty & value OH .xlsx")
